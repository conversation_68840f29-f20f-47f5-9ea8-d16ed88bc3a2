'use client';

import { useDeleteSupplier } from '@/apis/supplier/supplier.api';
import { ISupplier } from '@/apis/supplier/supplier.type';
import FormController from '@/components/common/FormController';
import { commonStatus } from '@/constants/sharedData/sharedData';
import { ROUTES } from '@/lib/routes';
import { formatDateTime } from '@/utils/format';
import { showToastSuccess } from '@/utils/toast-message';
import { yupResolver } from '@hookform/resolvers/yup';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Row,
    UncontrolledDropdown,
} from 'reactstrap';
import * as yup from 'yup';
import { InferType } from 'yup';

const supplierFormSchema = yup.object().shape({
    name: yup.string().required('Tên nhà cung cấp là trường bắt buộc'),
    code: yup.string().required('Mã nhà cung cấp là trường bắt buộc'),
    tax: yup.string().matches(/^[0-9]+$/, 'Mã số thuế chỉ được nhập số'),
    phoneNumber: yup.string().notRequired(),
    email: yup.string().notRequired(),
    description: yup.string().notRequired(),
    commonStatus: yup.number().notRequired(),
    country: yup.string().notRequired(),
    createdDateTime: yup.string().notRequired(),
    userNameCreated: yup.string().notRequired(),
    id: yup.string().notRequired(),
});

type SupplierFormData = InferType<typeof supplierFormSchema>;

interface FormSuppliersProps {
    page: string;
    onSubmit?: (data: ISupplier) => void;
    onClose?: () => void;
    initValue?: ISupplier;
}
const FormSuppliers = ({
    page,
    onSubmit,
    onClose,
    initValue,
}: FormSuppliersProps) => {
    const router = useRouter();

    const methods = useForm<SupplierFormData>({
        values: initValue,
        resolver: yupResolver(supplierFormSchema),
    });

    React.useEffect(() => {
        if (!initValue?.country) {
            methods.setValue('country', 'Việt Nam');
        }
    }, [methods, initValue]);

    const [image, setImage] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleIconClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (ev) => {
                setImage(ev.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleUpdate = (id: string) => {
        router.push(
            ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.UPDATE.replace(':id', id),
        );
    };
    const handleFormSubmit = (data: SupplierFormData) => {
        if (onSubmit) {
            const supplierData: ISupplier = {
                id: data.id || '',
                name: data.name || '',
                code: data.code || '',
                tax: data.tax || '',
                phoneNumber: data.phoneNumber || '',
                email: data.email || '',
                description: data.description || '',
                commonStatus: data.commonStatus || 0,
                country: data.country,
                createdDateTime: data.createdDateTime || '',
                userNameCreated: data.userNameCreated || '',
            };
            onSubmit(supplierData);
        }
    };
    const { mutate: deleteSupplier } = useDeleteSupplier({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa nhà cung cấp thành công',
                message:
                    'Thông tin nhà cung cấp đã được xóa thành công trong hệ thống.',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.INDEX);
        },
        onError: (error) => {
            const status = error.status;
            if (status === 400 || 401) {
                toast.warning(error.message);
                return;
            }
            toast.error(error.message);
        },
    });
    const handleDelete = () => {
        if (initValue?.id) {
            deleteSupplier({ ids: [initValue.id], isDeleted: false });
        }
    };
    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 20px 40px' }}>
                {page === 'chi-tiet' && (
                    <Col md={12} style={{ padding: '10px 40px 0px 40px' }}>
                        <div className='d-flex justify-content-end gap-3 align-items-center pe-3'>
                            <Button
                                className='d-flex align-items-center gap-2 btn-outline-primary hover-primary'
                                style={{
                                    padding: '4px 8px',
                                    fontSize: '12px',
                                    transition: 'all 0.2s ease',
                                    backgroundColor: 'transparent',
                                    border: '1px solid #0ab39c',
                                    color: '#0ab39c',
                                    borderRadius: '6px',
                                    height: '32px',
                                    fontWeight: 500,
                                }}
                                onClick={() => handleUpdate(initValue?.id)}
                            >
                                <i className='ri-pencil-line'></i>
                                Chỉnh sửa
                            </Button>
                            <UncontrolledDropdown>
                                <DropdownToggle
                                    tag='button'
                                    className='btn'
                                    style={{
                                        backgroundColor: '#0ab39c',
                                        border: 'none',
                                        padding: '4px',
                                        minWidth: '30px',
                                    }}
                                >
                                    <i
                                        className='ri-more-fill'
                                        style={{
                                            color: 'white',
                                        }}
                                    ></i>
                                </DropdownToggle>
                                <DropdownMenu end>
                                    <DropdownItem>
                                        <i className='ri-history-line me-2'></i>
                                        Nhật ký nhà cung cấp
                                    </DropdownItem>
                                    <DropdownItem
                                        className='text-danger'
                                        onClick={handleDelete}
                                    >
                                        <i className='ri-delete-bin-line me-2'></i>
                                        Xóa
                                    </DropdownItem>
                                </DropdownMenu>
                            </UncontrolledDropdown>
                        </div>
                    </Col>
                )}

                <Row className='mt-4' style={{ padding: '0px 40px 20px 40px' }}>
                    {(page === 'chi-tiet' || page === 'chinh-sua') && (
                        <Col
                            md={6}
                            style={{ marginTop: '10px', marginBottom: '10px' }}
                        >
                            <FormController
                                controlType='textInput'
                                name='code'
                                label='Mã nhà cung cấp'
                                placeholder='Nhập nhà cung cấp'
                                required={true}
                                readOnly={page === 'chi-tiet'}
                            />
                        </Col>
                    )}
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='name'
                            label='Tên nhà cung cấp'
                            placeholder='Nhập nhà cung cấp'
                            required={true}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='tax'
                            label='Mã số thuế'
                            placeholder='Nhập mã số thuế'
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>
                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            name='description'
                            controlType='textarea'
                            label='Mô tả chung'
                            readOnly={page === 'chi-tiet'}
                            placeholder='Mô tả chung về nhà cung cấp'
                            rows={8}
                        />
                    </Col>
                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <Label>Hình ảnh</Label>
                        <div
                            style={{
                                position: 'relative',
                                width: '300px',
                                height: '200px',
                                display: 'flex',
                                justifyContent: 'center',
                                cursor: 'pointer',
                            }}
                            onClick={handleIconClick}
                        >
                            {image ? (
                                <Image
                                    src={image}
                                    alt='Preview'
                                    fill
                                    style={{
                                        objectFit: 'cover',
                                        marginLeft: '30px',
                                    }}
                                />
                            ) : (
                                <i
                                    className='ri-image-add-fill'
                                    style={{ fontSize: '100px', color: '#ccc' }}
                                ></i>
                            )}
                            <input
                                type='file'
                                accept='image/*'
                                ref={fileInputRef}
                                style={{ display: 'none' }}
                                onChange={handleFileChange}
                            />
                        </div>
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='phoneNumber'
                            label='Số điện thoại'
                            placeholder='Nhập số điện thoại'
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='email'
                            label='Email'
                            placeholder='Nhập email'
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='country'
                            label='Quốc gia'
                            placeholder='Chọn quốc gia'
                            data={[
                                {
                                    label: 'Việt Nam',
                                    value: 'Việt Nam',
                                },
                            ]}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    {(page === 'chi-tiet' || page === 'chinh-sua') && (
                        <Col
                            md={6}
                            style={{ marginTop: '10px', marginBottom: '10px' }}
                        >
                            <FormController
                                controlType='select'
                                name='commonStatus'
                                label='Tình trạng'
                                data={commonStatus}
                                required={true}
                                readOnly={page === 'chi-tiet'}
                            />
                        </Col>
                    )}

                    {page === 'chi-tiet' && (
                        <>
                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='textInput'
                                    name='userNameCreated'
                                    label='Người tạo'
                                    placeholder='Nguyễn Văn A'
                                    readOnly={page === 'chi-tiet'}
                                />
                            </Col>

                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='textInput'
                                    name='createdDateTime'
                                    label='Ngày tạo'
                                    readOnly={page === 'chi-tiet'}
                                    value={formatDateTime(
                                        methods.watch('createdDateTime') || '',
                                    )}
                                />
                            </Col>
                        </>
                    )}
                </Row>

                {(page === 'tao-moi' || page === 'chinh-sua') && (
                    <div className='d-flex justify-content-end mt-4'>
                        <Button
                            color='danger'
                            className='me-2'
                            type='button'
                            onClick={onClose}
                        >
                            Hủy
                        </Button>
                        {page === 'tao-moi' && (
                            <Button
                                color='success'
                                type='button'
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Tạo mới
                            </Button>
                        )}
                        {page === 'chinh-sua' && (
                            <Button
                                color='success'
                                type='button'
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Lưu
                            </Button>
                        )}
                    </div>
                )}
            </Card>
        </FormProvider>
    );
};

export default FormSuppliers;
