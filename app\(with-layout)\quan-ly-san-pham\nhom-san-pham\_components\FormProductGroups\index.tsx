'use client';

import { useDeleteProductGroups } from '@/apis/product-group/product-group.api';
import { IProductGroup } from '@/apis/product-group/product-group.type';
import FormController from '@/components/common/FormController';
import { commonStatus } from '@/constants/sharedData/sharedData';
import useGetOptionFatherGroup from '@/hooks/useGetOptionFatherGroup';
import { ROUTES } from '@/lib/routes';
import { formatDateTime } from '@/utils/format';
import { showToastSuccess } from '@/utils/toast-message';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Row,
    UncontrolledDropdown,
} from 'reactstrap';

interface FormProductGroupsProps {
    page: string;
    onSubmit?: (data: IProductGroup) => void;
    onCancel?: () => void;
    initValue?: IProductGroup;
    onEdit?: (data: IProductGroup) => void;
}
const FormProductGroups = ({
    page,
    onSubmit,
    onCancel,
    initValue,
    onEdit,
}: FormProductGroupsProps) => {
    const router = useRouter();

    const methods = useForm<IProductGroup>({
        defaultValues: initValue
            ? {
                  ...initValue,
                  createdDateTime: initValue.createdDateTime
                      ? formatDateTime(initValue.createdDateTime)
                      : '',
              }
            : {},
    });

    const [image, setImage] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleIconClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (ev) => {
                setImage(ev.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const parents = useGetOptionFatherGroup();

    const handleUpdate = () => {
        router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCT_GROUPS.UPDATE);
    };

    const handleFormSubmit = (data: IProductGroup) => {
        if (onSubmit) {
            onSubmit(data);
        }
        if (onEdit) {
            onEdit(data);
        }
    };
    useEffect(() => {
        if (initValue?.status === 'Đang hoạt động') {
            methods.setValue('status', '1' as unknown as number);
        } else if (initValue?.status === 'Ngừng hoạt động') {
            methods.setValue('status', '2' as unknown as number);
        }
    }, [initValue?.status, methods]);
    const { mutate: deleteProductGroups } = useDeleteProductGroups({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa nhóm sản phẩm thành công',
                message:
                    'Thông tin nhóm sản phẩm đã được xóa thành công trong hệ thống.',
            });

            router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCT_GROUPS.INDEX);
        },
        onError: (error) => {
            const status = error.status;
            if (status === 400 || 401) {
                toast.warning(error.message);
                return;
            }
            toast.error(error.message);
        },
    });
    const handleDelete = () => {
        if (initValue?.id) {
            deleteProductGroups({ ids: [initValue.id], isDeleted: false });
        }
    };
    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 0px 40px' }}>
                {page === 'chi-tiet' && (
                    <Row>
                        <Col md={12} style={{ padding: '20px 40px 20px 40px' }}>
                            <div className='d-flex justify-content-end gap-3 align-items-center pe-3'>
                                <Button
                                    className='d-flex align-items-center gap-2 btn-outline-primary hover-primary'
                                    style={{
                                        padding: '4px 8px',
                                        fontSize: '12px',
                                        transition: 'all 0.2s ease',
                                        backgroundColor: 'transparent',
                                        border: '1px solid #0ab39c',
                                        color: '#0ab39c',
                                        borderRadius: '6px',
                                        height: '32px',
                                        fontWeight: 500,
                                    }}
                                    onClick={handleUpdate}
                                >
                                    <i className='ri-pencil-line'></i>
                                    Chỉnh sửa
                                </Button>
                                <UncontrolledDropdown>
                                    <DropdownToggle
                                        tag='button'
                                        className='btn'
                                        style={{
                                            backgroundColor: '#0ab39c',
                                            border: 'none',
                                            padding: '4px',
                                            minWidth: '30px',
                                        }}
                                    >
                                        <i
                                            className='ri-more-fill'
                                            style={{
                                                color: 'white',
                                            }}
                                        ></i>
                                    </DropdownToggle>
                                    <DropdownMenu end>
                                        <DropdownItem>
                                            <i className='ri-history-line me-2'></i>
                                            Nhật ký nhóm sản phẩm
                                        </DropdownItem>
                                        <DropdownItem
                                            className='text-danger'
                                            onClick={handleDelete}
                                        >
                                            <i className='ri-delete-bin-line me-2'></i>
                                            Xóa
                                        </DropdownItem>
                                    </DropdownMenu>
                                </UncontrolledDropdown>
                            </div>
                        </Col>
                    </Row>
                )}

                <Row style={{ padding: '0px 40px 20px 40px' }}>
                    {page === 'chi-tiet' && (
                        <Col
                            md={6}
                            style={{ marginTop: '10px', marginBottom: '10px' }}
                        >
                            <FormController
                                controlType='textInput'
                                name='code'
                                label='Mã sản phẩm'
                                required={true}
                                readOnly={page === 'chi-tiet'}
                            />
                        </Col>
                    )}
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='name'
                            label='Tên nhóm sản phẩm'
                            placeholder='Nhập tên nhóm sản phẩm'
                            required={true}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='parentId'
                            label='Tên nhóm cha'
                            data={parents}
                            placeholder='Chọn tên nhóm cha'
                            required={false}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            name='description'
                            controlType='textarea'
                            label='Mô tả chung'
                            placeholder='Mô tả chung về nhóm sản phẩm'
                            readOnly={page === 'chi-tiet'}
                            rows={8}
                        />
                    </Col>
                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <Label>Hình ảnh</Label>
                        <div
                            style={{
                                position: 'relative',
                                width: '300px',
                                height: '200px',
                                display: 'flex',
                                justifyContent: 'center',
                                cursor: 'pointer',
                            }}
                            onClick={handleIconClick}
                        >
                            {image ? (
                                <Image
                                    src={image}
                                    alt='Preview'
                                    fill
                                    style={{
                                        objectFit: 'cover',
                                        marginLeft: '30px',
                                    }}
                                />
                            ) : (
                                <i
                                    className='ri-image-add-fill'
                                    style={{
                                        fontSize: '100px',
                                        color: '#ccc',
                                    }}
                                ></i>
                            )}
                            <input
                                type='file'
                                accept='image/*'
                                ref={fileInputRef}
                                style={{ display: 'none' }}
                                onChange={handleFileChange}
                            />
                        </div>
                    </Col>
                    {(page === 'chi-tiet' || page === 'chinh-sua') && (
                        <>
                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='select'
                                    name='status'
                                    label='Tình trạng'
                                    required={true}
                                    data={commonStatus}
                                    readOnly={page === 'chi-tiet'}
                                />
                            </Col>
                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            ></Col>
                        </>
                    )}

                    {page === 'chi-tiet' && (
                        <>
                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='textInput'
                                    name='userNameCreated'
                                    label='Người tạo'
                                    placeholder='Nguyễn Văn A'
                                    readOnly={page === 'chi-tiet'}
                                />
                            </Col>
                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='textInput'
                                    name='createdDateTime'
                                    label='Ngày tạo'
                                    readOnly={page === 'chi-tiet'}
                                />
                            </Col>
                        </>
                    )}
                </Row>

                <Row style={{ padding: '0px 40px 20px 40px' }}>
                    {page === 'tao-moi' || page === 'chinh-sua' ? (
                        <div className='d-flex justify-content-end mt-4'>
                            <Button
                                color='danger'
                                className='me-2'
                                type='button'
                                onClick={onCancel}
                            >
                                Hủy
                            </Button>
                            {page === 'tao-moi' && (
                                <Button
                                    color='success'
                                    onClick={methods.handleSubmit(
                                        handleFormSubmit,
                                    )}
                                >
                                    Tạo mới
                                </Button>
                            )}
                            {page === 'chinh-sua' && (
                                <Button
                                    color='success'
                                    onClick={methods.handleSubmit(
                                        handleFormSubmit,
                                    )}
                                >
                                    Lưu
                                </Button>
                            )}
                        </div>
                    ) : (
                        <div style={{ height: '16px' }}></div>
                    )}
                </Row>
            </Card>
        </FormProvider>
    );
};

export default FormProductGroups;
